<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Models\User;
use App\Models\Organization;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\UseCases\ChatBot\Flow\SaveFullFlow;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\Fixtures\ChatBot\PizzariaFlowData;
use Tests\TestCase;

class PizzariaFlowNavigationTest extends TestCase
{
    use RefreshDatabase;

    private ProcessFlowStep $processFlowStep;
    private SaveFullFlow $saveFullFlow;
    private User $user;
    private Organization $organization;
    private Flow $pizzariaFlow;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Auth::login($this->user);

        $this->processFlowStep = app()->make(ProcessFlowStep::class);
        $this->saveFullFlow = app()->make(SaveFullFlow::class);

        // Create the pizzaria flow for testing
        $this->createPizzariaFlow();
    }

    private function createPizzariaFlow(): void
    {
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = new SaveFullFlowRequest();
        $request->replace($flowData);

        $this->pizzariaFlow = $this->saveFullFlow->perform($request);
    }

    public function test_complete_pizzaria_flow_navigation_happy_path()
    {
        // Arrange - Create conversation starting at welcome step
        $conversation = $this->createConversation();
        $welcomeStep = $this->pizzariaFlow->getInitialStep();
        $conversation->current_step = $welcomeStep;
        $conversation->current_step_id = $welcomeStep->id;

        // Step 1: Welcome - User clicks "Fazer Pedido"
        $interaction1 = $this->createButtonInteraction($conversation, 'Fazer Pedido');
        $result1 = $this->processFlowStep->perform($conversation, $interaction1);

        $this->assertEquals('interactive', $result1['type']);
        $this->assertEquals('show_options', $result1['action']);
        $this->assertArrayHasKey('options', $result1);
        $this->assertFalse($result1['move_to_next']);

        // Step 2: Size Selection - User selects "M - R$ 35,00"
        $conversation->moveToNextStep($this->getStepByPosition(1)); // choose_size
        $interaction2 = $this->createButtonInteraction($conversation, 'M - R$ 35,00', [
            'size' => 'M',
            'price' => 35.00
        ]);
        $result2 = $this->processFlowStep->perform($conversation, $interaction2);

        $this->assertEquals('interactive', $result2['type']);
        $this->assertEquals('process_selection', $result2['action']);
        $this->assertArrayHasKey('selection', $result2);
        $this->assertTrue($result2['move_to_next']);

        // Step 3: First Flavor - User selects "Calabresa"
        $conversation->moveToNextStep($this->getStepByPosition(3)); // choose_first_flavor
        $interaction3 = $this->createButtonInteraction($conversation, 'Calabresa', [
            'flavor' => 'Calabresa'
        ]);
        $result3 = $this->processFlowStep->perform($conversation, $interaction3);

        $this->assertEquals('interactive', $result3['type']);
        $this->assertTrue($result3['move_to_next']);

        // Step 4: Second Flavor - User selects "Chocolate"
        $conversation->moveToNextStep($this->getStepByPosition(4)); // choose_second_flavor
        $interaction4 = $this->createButtonInteraction($conversation, 'Chocolate', [
            'flavor' => 'Chocolate'
        ]);
        $result4 = $this->processFlowStep->perform($conversation, $interaction4);

        $this->assertEquals('interactive', $result4['type']);
        $this->assertTrue($result4['move_to_next']);

        // Step 5: Order Summary - Message step (auto-advance)
        $conversation->moveToNextStep($this->getStepByPosition(5)); // order_summary
        $interaction5 = $this->createTextInteraction($conversation, 'ok');
        $result5 = $this->processFlowStep->perform($conversation, $interaction5);

        $this->assertEquals('message', $result5['type']);
        $this->assertEquals('send_message', $result5['action']);
        $this->assertTrue($result5['move_to_next']);

        // Step 6: Order Confirmation - User clicks "Confirmar"
        $conversation->moveToNextStep($this->getStepByPosition(6)); // order_confirmation
        $interaction6 = $this->createButtonInteraction($conversation, 'Confirmar');
        $result6 = $this->processFlowStep->perform($conversation, $interaction6);

        $this->assertEquals('interactive', $result6['type']);
        $this->assertTrue($result6['move_to_next']);

        // Step 7: Order Finalized - Final message
        $conversation->moveToNextStep($this->getStepByPosition(7)); // order_finalized
        $interaction7 = $this->createTextInteraction($conversation, 'obrigado');
        $result7 = $this->processFlowStep->perform($conversation, $interaction7);

        $this->assertEquals('message', $result7['type']);
        $this->assertNull($result7['next_step']); // Should be null for ending step

        // Verify conversation is finished
        $this->assertTrue($conversation->is_finished);
    }

    public function test_pizzaria_flow_navigation_with_family_size()
    {
        // Arrange
        $conversation = $this->createConversation();
        $welcomeStep = $this->pizzariaFlow->getInitialStep();
        $conversation->current_step = $welcomeStep;

        // Navigate to family size selection
        $conversation->moveToNextStep($this->getStepByPosition(2)); // choose_size_family
        $interaction = $this->createButtonInteraction($conversation, 'F - R$ 65,00', [
            'size' => 'F',
            'price' => 65.00
        ]);

        // Act
        $result = $this->processFlowStep->perform($conversation, $interaction);

        // Assert
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('process_selection', $result['action']);
        $this->assertEquals(['size' => 'F', 'price' => 65.00], $result['selection']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_pizzaria_flow_navigation_skip_second_flavor()
    {
        // Arrange
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(4); // choose_second_flavor

        // User chooses to skip second flavor
        $interaction = $this->createButtonInteraction($conversation, 'Não, obrigado');

        // Act
        $result = $this->processFlowStep->perform($conversation, $interaction);

        // Assert
        $this->assertEquals('interactive', $result['type']);
        $this->assertTrue($result['move_to_next']);
        // Should move to order summary step
        $this->assertEquals($this->getStepByPosition(5)->id, $result['next_step']);
    }

    public function test_pizzaria_flow_navigation_cancel_order()
    {
        // Arrange
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(6); // order_confirmation

        // User chooses to cancel order
        $interaction = $this->createButtonInteraction($conversation, 'Cancelar');

        // Act
        $result = $this->processFlowStep->perform($conversation, $interaction);

        // Assert
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('process_selection', $result['action']);
        // Should handle cancellation logic
        $this->assertArrayHasKey('selection', $result);
    }

    public function test_pizzaria_flow_navigation_modify_order()
    {
        // Arrange
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(6); // order_confirmation

        // User chooses to modify order
        $interaction = $this->createButtonInteraction($conversation, 'Modificar');

        // Act
        $result = $this->processFlowStep->perform($conversation, $interaction);

        // Assert
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('process_selection', $result['action']);
        // Should restart flow or go back to beginning
        $this->assertArrayHasKey('selection', $result);
    }

    public function test_pizzaria_flow_handles_invalid_step_gracefully()
    {
        // Arrange
        $conversation = $this->createConversation();
        $conversation->current_step = null; // Invalid state

        $interaction = $this->createTextInteraction($conversation, 'test');

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No current step found for conversation');

        $this->processFlowStep->perform($conversation, $interaction);
    }

    public function test_pizzaria_flow_step_validation()
    {
        // Test that all steps in the flow are properly configured
        $steps = $this->pizzariaFlow->steps;

        $this->assertCount(8, $steps);

        // Verify initial step
        $initialStep = $this->pizzariaFlow->getInitialStep();
        $this->assertNotNull($initialStep);
        $this->assertEquals('welcome', $initialStep->step);
        $this->assertEquals(0, $initialStep->position);

        // Verify ending step
        $endingStep = $this->pizzariaFlow->getEndingStep();
        $this->assertNotNull($endingStep);
        $this->assertEquals('order_finalized', $endingStep->step);
        $this->assertEquals(7, $endingStep->position);

        // Verify step navigation chain
        foreach ($steps as $step) {
            if (!$step->is_ending_step && $step->next_step !== null) {
                $nextStep = $this->getStepByPosition($step->next_step);
                $this->assertNotNull($nextStep, "Next step {$step->next_step} not found for step {$step->step}");
            }
        }
    }

    // Helper methods

    private function createConversation(): WhatsAppConversation
    {
        return new WhatsAppConversation(
            id: null,
            organization_id: $this->organization->id,
            phone_number_id: '123456789',
            whatsapp_phone_number: '+5511999999999',
            flow_id: $this->pizzariaFlow->id,
            current_step_id: null,
            is_finished: false,
            conversation_data: [],
            flow: $this->pizzariaFlow,
            current_step: null
        );
    }

    private function createButtonInteraction(
        WhatsAppConversation $conversation,
        string $buttonText,
        array $callbackData = []
    ): WhatsAppInteraction {
        return new WhatsAppInteraction(
            id: null,
            organization_id: $this->organization->id,
            conversation_id: $conversation->id,
            whatsapp_message_id: 'msg_' . uniqid(),
            whatsapp_message_type: 'interactive',
            message_content: json_encode([
                'type' => 'button_reply',
                'button_reply' => [
                    'id' => 'btn_' . uniqid(),
                    'title' => $buttonText
                ]
            ]),
            step_id: $conversation->current_step_id,
            result: null,
            conversation: $conversation,
            step: $conversation->current_step
        );
    }

    private function createTextInteraction(
        WhatsAppConversation $conversation,
        string $text
    ): WhatsAppInteraction {
        return new WhatsAppInteraction(
            id: null,
            organization_id: $this->organization->id,
            conversation_id: $conversation->id,
            whatsapp_message_id: 'msg_' . uniqid(),
            whatsapp_message_type: 'text',
            message_content: json_encode([
                'type' => 'text',
                'text' => ['body' => $text]
            ]),
            step_id: $conversation->current_step_id,
            result: null,
            conversation: $conversation,
            step: $conversation->current_step
        );
    }

    private function getStepByPosition(int $position): ?Step
    {
        return $this->pizzariaFlow->getStepByPosition($position);
    }

    public function test_pizzaria_flow_conditional_navigation_with_callback_data()
    {
        // Test that callback data is properly processed during navigation
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(1); // choose_size

        // Create interaction with callback data
        $interaction = $this->createButtonInteraction($conversation, 'G - R$ 45,00', [
            'size' => 'G',
            'price' => 45.00
        ]);

        // Mock the callback data in the interaction
        $interaction->message_content = json_encode([
            'type' => 'button_reply',
            'button_reply' => [
                'id' => 'btn_size_g',
                'title' => 'G - R$ 45,00'
            ],
            'callback_data' => json_encode(['size' => 'G', 'price' => 45.00])
        ]);

        $result = $this->processFlowStep->perform($conversation, $interaction);

        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('process_selection', $result['action']);
        $this->assertArrayHasKey('selection', $result);

        // Verify callback data is included in the result
        if (isset($result['selection'])) {
            $this->assertIsArray($result['selection']);
        }
    }

    public function test_pizzaria_flow_back_navigation()
    {
        // Test navigation back to previous steps
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(2); // choose_size_family

        // User clicks "Voltar" button
        $interaction = $this->createButtonInteraction($conversation, 'Voltar');

        $result = $this->processFlowStep->perform($conversation, $interaction);

        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('process_selection', $result['action']);

        // Should navigate back to previous step
        $this->assertTrue($result['move_to_next']);
    }

    public function test_pizzaria_flow_variable_substitution_in_messages()
    {
        // Test that variables are properly substituted in step messages
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(5); // order_summary

        // Set some conversation data for variable substitution
        $conversation->conversation_data = [
            'pizza_size' => 'M',
            'pizza_flavors' => 'Calabresa, Chocolate',
            'total_price' => '35,00',
            'client_name' => 'João'
        ];

        $interaction = $this->createTextInteraction($conversation, 'ok');

        $result = $this->processFlowStep->perform($conversation, $interaction);

        $this->assertEquals('message', $result['type']);
        $this->assertEquals('send_message', $result['action']);
        $this->assertArrayHasKey('message', $result);

        // The message should contain the step text (variables will be substituted by the message processor)
        $this->assertStringContainsString('Resumo do seu pedido', $result['message']);
    }

    public function test_pizzaria_flow_handles_unexpected_input()
    {
        // Test how the flow handles unexpected user input
        $conversation = $this->createConversation();
        $conversation->current_step = $this->getStepByPosition(1); // choose_size (interactive step)

        // Send text instead of button click
        $interaction = $this->createTextInteraction($conversation, 'pizza grande');

        $result = $this->processFlowStep->perform($conversation, $interaction);

        // Should still show options since no valid button was selected
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('show_options', $result['action']);
        $this->assertFalse($result['move_to_next']);
    }
}

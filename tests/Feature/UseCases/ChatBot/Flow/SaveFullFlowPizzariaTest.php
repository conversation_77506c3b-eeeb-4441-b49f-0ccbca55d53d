<?php

namespace Tests\Feature\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Models\User;
use App\Models\Organization;
use App\UseCases\ChatBot\Flow\SaveFullFlow;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\Fixtures\ChatBot\PizzariaFlowData;
use Tests\TestCase;

class SaveFullFlowPizzariaTest extends TestCase
{
    use RefreshDatabase;

    private SaveFullFlow $saveFullFlow;
    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Auth::login($this->user);

        $this->saveFullFlow = app()->make(SaveFullFlow::class);
    }

    public function test_save_complete_pizzaria_flow_successfully()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act
        $flow = $this->saveFullFlow->perform($request);

        // Assert
        $this->assertInstanceOf(Flow::class, $flow);
        $this->assertNotNull($flow->id);
        $this->assertEquals('Fluxo Pizzaria', $flow->name);
        $this->assertEquals($this->organization->id, $flow->organization_id);
        $this->assertEquals(8, $flow->steps_count); // 8 steps no fluxo
        $this->assertNotNull($flow->json);

        // Verify JSON contains all flow data
        $savedJson = json_decode($flow->json, true);
        $this->assertArrayHasKey('flow', $savedJson);
        $this->assertArrayHasKey('steps', $savedJson);
        $this->assertCount(8, $savedJson['steps']);

        // Verify variables are saved
        $this->assertNotNull($flow->variables);
        $this->assertArrayHasKey('pizza_size', $flow->variables);
        $this->assertArrayHasKey('pizza_flavors', $flow->variables);
        $this->assertArrayHasKey('total_price', $flow->variables);
        $this->assertArrayHasKey('client_name', $flow->variables);
    }

    public function test_save_flow_with_all_steps_and_components()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act
        $flow = $this->saveFullFlow->perform($request);

        // Assert - Verify flow was saved to database
        $this->assertDatabaseHas('flows', [
            'id' => $flow->id,
            'organization_id' => $this->organization->id,
            'name' => 'Fluxo Pizzaria',
            'description' => 'Fluxo completo para pedidos de pizza com escolha de tamanho e sabores',
            'steps_count' => 8,
            'is_default_flow' => false,
            'inactivity_minutes' => 30,
            'ending_conversation_message' => 'Obrigado por escolher nossa pizzaria! Seu pedido foi registrado.',
            'version' => '1.0',
            'status' => 'active'
        ]);

        // Verify all steps were saved
        $this->assertDatabaseCount('steps', 8);

        // Verify specific steps
        $this->assertDatabaseHas('steps', [
            'flow_id' => $flow->id,
            'step' => 'welcome',
            'position' => 0,
            'is_initial_step' => true,
            'is_ending_step' => false,
            'step_type' => 'interactive'
        ]);

        $this->assertDatabaseHas('steps', [
            'flow_id' => $flow->id,
            'step' => 'order_finalized',
            'position' => 7,
            'is_initial_step' => false,
            'is_ending_step' => true,
            'step_type' => 'message'
        ]);

        // Verify components were saved
        $this->assertDatabaseCount('components', 8); // One component per step

        // Verify buttons were saved (count all buttons from all steps)
        $expectedButtonCount = 1 + 3 + 2 + 3 + 3 + 0 + 3 + 0; // 15 buttons total
        $this->assertDatabaseCount('buttons', $expectedButtonCount);

        // Verify parameters were saved
        $this->assertDatabaseCount('parameters', 4); // 3 in summary + 1 in finalized
    }

    public function test_save_flow_with_proper_step_navigation()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act
        $flow = $this->saveFullFlow->perform($request);

        // Assert - Verify step navigation
        $this->assertDatabaseHas('steps', [
            'flow_id' => $flow->id,
            'step' => 'welcome',
            'position' => 0,
            'next_step' => 1,
            'earlier_step' => null
        ]);

        $this->assertDatabaseHas('steps', [
            'flow_id' => $flow->id,
            'step' => 'choose_size',
            'position' => 1,
            'next_step' => 2,
            'earlier_step' => 0
        ]);

        $this->assertDatabaseHas('steps', [
            'flow_id' => $flow->id,
            'step' => 'order_finalized',
            'position' => 7,
            'next_step' => null,
            'earlier_step' => null
        ]);
    }

    public function test_save_flow_with_interactive_buttons()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act
        $flow = $this->saveFullFlow->perform($request);

        // Assert - Verify specific buttons
        $this->assertDatabaseHas('buttons', [
            'text' => 'Fazer Pedido',
            'type' => 'REPLY',
            'internal_type' => 'navigation',
            'internal_data' => 'next_step'
        ]);

        $this->assertDatabaseHas('buttons', [
            'text' => 'P - R$ 25,00',
            'type' => 'REPLY',
            'internal_type' => 'condition',
            'internal_data' => 'size_p'
        ]);

        $this->assertDatabaseHas('buttons', [
            'text' => 'Calabresa',
            'type' => 'REPLY',
            'internal_type' => 'condition',
            'internal_data' => 'flavor_calabresa'
        ]);

        $this->assertDatabaseHas('buttons', [
            'text' => 'Confirmar',
            'type' => 'REPLY',
            'internal_type' => 'command',
            'internal_data' => 'confirm_order'
        ]);
    }

    public function test_save_flow_with_callback_data()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act
        $flow = $this->saveFullFlow->perform($request);

        // Assert - Verify callback data is properly saved
        $this->assertDatabaseHas('buttons', [
            'text' => 'P - R$ 25,00',
            'callback_data' => json_encode(['size' => 'P', 'price' => 25.00])
        ]);

        $this->assertDatabaseHas('buttons', [
            'text' => 'M - R$ 35,00',
            'callback_data' => json_encode(['size' => 'M', 'price' => 35.00])
        ]);

        $this->assertDatabaseHas('buttons', [
            'text' => 'Calabresa',
            'callback_data' => json_encode(['flavor' => 'Calabresa'])
        ]);
    }

    public function test_save_flow_with_parameters_for_variable_substitution()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act
        $flow = $this->saveFullFlow->perform($request);

        // Assert - Verify parameters for variable substitution
        $this->assertDatabaseHas('parameters', [
            'type' => 'text',
            'value' => 'pizza_size',
            'placeholder' => 'Não informado',
            'index' => 0
        ]);

        $this->assertDatabaseHas('parameters', [
            'type' => 'text',
            'value' => 'pizza_flavors',
            'placeholder' => 'Nenhum sabor selecionado',
            'index' => 1
        ]);

        $this->assertDatabaseHas('parameters', [
            'type' => 'text',
            'value' => 'client_name',
            'placeholder' => 'Cliente',
            'index' => 0
        ]);
    }

    public function test_save_simple_flow_successfully()
    {
        // Arrange
        $flowData = PizzariaFlowData::getSimpleFlowData();

        // Test the data validation first
        $request = new SaveFullFlowRequest();
        $request->replace($flowData);
        $validator = \Illuminate\Support\Facades\Validator::make($flowData, $request->rules());
        $request->withValidator($validator);

        $this->assertTrue($validator->passes(), 'Flow data should be valid: ' . json_encode($validator->errors()->toArray()));

        // Test that we can create the flow domain objects
        $flowFactory = app()->make(\App\Factories\ChatBot\FlowFactory::class);
        $flowDomain = $flowFactory->buildFromSaveFullFlow(
            $flowData['flow'],
            json_encode($flowData),
            count($flowData['steps']),
            $this->organization->id,
            null
        );

        $this->assertInstanceOf(\App\Domains\ChatBot\Flow::class, $flowDomain);
        $this->assertEquals('Fluxo Pizzaria Simples', $flowDomain->name);
        $this->assertEquals(2, $flowDomain->steps_count);
        $this->assertEquals($this->organization->id, $flowDomain->organization_id);

        // Test that the JSON structure is correct
        $this->assertNotNull($flowDomain->json);
        $savedJson = json_decode($flowDomain->json, true);
        $this->assertArrayHasKey('flow', $savedJson);
        $this->assertArrayHasKey('steps', $savedJson);
        $this->assertCount(2, $savedJson['steps']);
    }

    public function test_update_existing_flow()
    {
        // Arrange - First create a flow
        $flowData = PizzariaFlowData::getSimpleFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));
        $originalFlow = $this->saveFullFlow->perform($request);

        // Modify the flow data for update
        $flowData['flow']['id'] = $originalFlow->id;
        $flowData['flow']['name'] = 'Fluxo Pizzaria Atualizado';
        $flowData['flow']['description'] = 'Descrição atualizada';

        $updateRequest = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $updateRequest->setContainer(app());
        $updateRequest->setRedirector(app('redirect'));

        // Act
        $updatedFlow = $this->saveFullFlow->perform($updateRequest);

        // Assert
        $this->assertEquals($originalFlow->id, $updatedFlow->id);
        $this->assertEquals('Fluxo Pizzaria Atualizado', $updatedFlow->name);
        $this->assertEquals('Descrição atualizada', $updatedFlow->description);

        // Verify only one flow exists in database
        $this->assertDatabaseCount('flows', 1);
        $this->assertDatabaseHas('flows', [
            'id' => $originalFlow->id,
            'name' => 'Fluxo Pizzaria Atualizado'
        ]);
    }

    public function test_flow_validation_passes_for_valid_pizzaria_flow()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();
        $request = SaveFullFlowRequest::create('/test', 'POST', $flowData);
        $request->setContainer(app());
        $request->setRedirector(app('redirect'));

        // Act & Assert - Should not throw any validation exceptions
        $flow = $this->saveFullFlow->perform($request);
        $this->assertInstanceOf(Flow::class, $flow);

        // Verify flow has proper structure
        $this->assertTrue($flow->getInitialStep() !== null);
        $this->assertTrue($flow->getEndingStep() !== null);
        $this->assertEquals(0, $flow->getInitialStep()->position);
        $this->assertEquals(7, $flow->getEndingStep()->position);
    }
}

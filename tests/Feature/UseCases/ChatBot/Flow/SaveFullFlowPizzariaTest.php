<?php

namespace Tests\Feature\UseCases\ChatBot\Flow;

use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\Fixtures\ChatBot\PizzariaFlowData;
use Tests\TestCase;

class SaveFullFlowPizzariaTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Auth::login($this->user);
    }

    public function test_pizzaria_flow_data_validation_passes()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();

        // Validate the data
        $request = new SaveFullFlowRequest();
        $request->replace($flowData);
        $validator = \Illuminate\Support\Facades\Validator::make($flowData, $request->rules());
        $request->withValidator($validator);

        // Assert
        $this->assertTrue($validator->passes(), 'Complete pizzaria flow data should be valid: ' . json_encode($validator->errors()->toArray()));

        // Verify flow structure
        $this->assertArrayHasKey('flow', $flowData);
        $this->assertArrayHasKey('steps', $flowData);
        $this->assertCount(8, $flowData['steps']);
        $this->assertEquals('Fluxo Pizzaria', $flowData['flow']['name']);
    }

    public function test_pizzaria_flow_domain_creation()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();

        // Test that we can create the flow domain objects
        $flowFactory = app()->make(\App\Factories\ChatBot\FlowFactory::class);
        $flow = $flowFactory->buildFromSaveFullFlow(
            $flowData['flow'],
            json_encode($flowData),
            count($flowData['steps']),
            $this->organization->id,
            null
        );

        // Assert
        $this->assertInstanceOf(\App\Domains\ChatBot\Flow::class, $flow);
        $this->assertEquals('Fluxo Pizzaria', $flow->name);
        $this->assertEquals($this->organization->id, $flow->organization_id);
        $this->assertEquals(8, $flow->steps_count);
        $this->assertNotNull($flow->json);

        // Verify JSON structure
        $savedJson = json_decode($flow->json, true);
        $this->assertArrayHasKey('flow', $savedJson);
        $this->assertArrayHasKey('steps', $savedJson);
        $this->assertCount(8, $savedJson['steps']);
    }

    public function test_pizzaria_flow_steps_structure()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();

        // Create flow domain
        $flowFactory = app()->make(\App\Factories\ChatBot\FlowFactory::class);
        $flow = $flowFactory->buildFromSaveFullFlow(
            $flowData['flow'],
            json_encode($flowData),
            count($flowData['steps']),
            $this->organization->id,
            null
        );

        $savedJson = json_decode($flow->json, true);
        $steps = $savedJson['steps'];

        // Verify specific steps exist
        $stepNames = array_column($steps, 'step');
        $expectedSteps = [
            'welcome',
            'choose_size',
            'choose_size_family',
            'choose_first_flavor',
            'choose_second_flavor',
            'order_summary',
            'order_confirmation',
            'order_finalized'
        ];

        foreach ($expectedSteps as $expectedStep) {
            $this->assertContains($expectedStep, $stepNames, "Step '{$expectedStep}' should exist in flow");
        }

        // Verify initial and final steps
        $welcomeStep = collect($steps)->firstWhere('step', 'welcome');
        $this->assertTrue($welcomeStep['is_initial_step']);
        $this->assertEquals(0, $welcomeStep['position']);

        $finalStep = collect($steps)->firstWhere('step', 'order_finalized');
        $this->assertTrue($finalStep['is_ending_step']);
        $this->assertEquals(7, $finalStep['position']);
    }

    public function test_pizzaria_flow_buttons_and_navigation()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();

        // Create flow domain
        $flowFactory = app()->make(\App\Factories\ChatBot\FlowFactory::class);
        $flow = $flowFactory->buildFromSaveFullFlow(
            $flowData['flow'],
            json_encode($flowData),
            count($flowData['steps']),
            $this->organization->id,
            null
        );

        $savedJson = json_decode($flow->json, true);
        $steps = $savedJson['steps'];

        // Verify welcome step has "Fazer Pedido" button
        $welcomeStep = collect($steps)->firstWhere('step', 'welcome');
        $this->assertArrayHasKey('component', $welcomeStep);
        $this->assertArrayHasKey('buttons', $welcomeStep['component']);
        $this->assertCount(1, $welcomeStep['component']['buttons']);
        $this->assertEquals('Fazer Pedido', $welcomeStep['component']['buttons'][0]['text']);

        // Verify size selection step has P/M/G buttons
        $sizeStep = collect($steps)->firstWhere('step', 'choose_size');
        $this->assertArrayHasKey('buttons', $sizeStep['component']);
        $this->assertCount(3, $sizeStep['component']['buttons']);

        $buttonTexts = array_column($sizeStep['component']['buttons'], 'text');
        $this->assertContains('P - R$ 25,00', $buttonTexts);
        $this->assertContains('M - R$ 35,00', $buttonTexts);
        $this->assertContains('G - R$ 45,00', $buttonTexts);

        // Verify buttons have callback data
        foreach ($sizeStep['component']['buttons'] as $button) {
            $this->assertArrayHasKey('callback_data', $button);
            $this->assertNotEmpty($button['callback_data']);
        }
    }

    public function test_pizzaria_flow_variables_and_parameters()
    {
        // Arrange
        $flowData = PizzariaFlowData::getCompleteFlowData();

        // Create flow domain
        $flowFactory = app()->make(\App\Factories\ChatBot\FlowFactory::class);
        $flow = $flowFactory->buildFromSaveFullFlow(
            $flowData['flow'],
            json_encode($flowData),
            count($flowData['steps']),
            $this->organization->id,
            null
        );

        $savedJson = json_decode($flow->json, true);

        // Verify flow variables
        $this->assertArrayHasKey('variables', $savedJson['flow']);
        $variables = $savedJson['flow']['variables'];
        $this->assertArrayHasKey('pizza_size', $variables);
        $this->assertArrayHasKey('pizza_flavors', $variables);
        $this->assertArrayHasKey('total_price', $variables);
        $this->assertArrayHasKey('client_name', $variables);

        // Verify order summary step has parameters for variable substitution
        $summaryStep = collect($savedJson['steps'])->firstWhere('step', 'order_summary');
        $this->assertArrayHasKey('component', $summaryStep);
        $this->assertArrayHasKey('parameters', $summaryStep['component']);
        $this->assertNotEmpty($summaryStep['component']['parameters']);

        // Verify text contains variable placeholders
        $this->assertStringContainsString('{{pizza.size}}', $summaryStep['component']['text']);
        $this->assertStringContainsString('{{pizza.flavors}}', $summaryStep['component']['text']);
        $this->assertStringContainsString('{{pizza.total_price}}', $summaryStep['component']['text']);
    }

    public function test_simple_pizzaria_flow_validation()
    {
        // Arrange
        $flowData = PizzariaFlowData::getSimpleFlowData();

        // Validate the data
        $request = new SaveFullFlowRequest();
        $request->replace($flowData);
        $validator = \Illuminate\Support\Facades\Validator::make($flowData, $request->rules());
        $request->withValidator($validator);

        // Assert
        $this->assertTrue($validator->passes(), 'Simple flow data should be valid: ' . json_encode($validator->errors()->toArray()));

        // Test domain creation
        $flowFactory = app()->make(\App\Factories\ChatBot\FlowFactory::class);
        $flow = $flowFactory->buildFromSaveFullFlow(
            $flowData['flow'],
            json_encode($flowData),
            count($flowData['steps']),
            $this->organization->id,
            null
        );

        $this->assertInstanceOf(\App\Domains\ChatBot\Flow::class, $flow);
        $this->assertEquals('Fluxo Pizzaria Simples', $flow->name);
        $this->assertEquals(2, $flow->steps_count);

        // Verify JSON structure
        $savedJson = json_decode($flow->json, true);
        $this->assertArrayHasKey('flow', $savedJson);
        $this->assertArrayHasKey('steps', $savedJson);
        $this->assertCount(2, $savedJson['steps']);
    }
}

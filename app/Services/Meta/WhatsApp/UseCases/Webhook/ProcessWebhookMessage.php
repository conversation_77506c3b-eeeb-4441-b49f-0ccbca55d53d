<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Domains\WhatsApp\ChangeValueMessage;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\UseCases\ChatBot\ExchangedMessage\SaveExchangedMessagesFromWebhook;
use App\Helpers\DBLog;

class ProcessWebhookMessage
{
    private ChatBotService $chatBotService;
    private SaveExchangedMessagesFromWebhook $saveExchangedMessagesFromWebhook;

    public function __construct(
        ChatBotService $chatBotService,
        SaveExchangedMessagesFromWebhook $saveExchangedMessagesFromWebhook
    ) {
        $this->chatBotService = $chatBotService;
        $this->saveExchangedMessagesFromWebhook = $saveExchangedMessagesFromWebhook;
    }

    /**
     * Process webhook message using ChatBot service
     *
     * @param ChangeValue $changeValue
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @param int|null $webhookLogId
     * @return array
     */
    public function perform(ChangeValue $changeValue, Organization $organization, PhoneNumber $phoneNumber, ?int $webhookLogId = null): array
    {
        try {
            if (!$changeValue->hasMessages()) {
                return [
                    'success' => false,
                    'error' => 'No messages found in change value',
                    'processed' => 0
                ];
            }

            $exchangedMessagesResult = $this->saveExchangedMessagesFromWebhook->perform(
                $changeValue,
                $organization,
                $phoneNumber,
                $webhookLogId
            );

            if (!$phoneNumber->shouldProcessChatBot()) {
                DBLog::logInfo(
                    "ChatBot processing skipped for phone number",
                    "ProcessWebhookMessage",
                    $organization->id,
                    null,
                    [
                        'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                        'reason' => $phoneNumber->getSkipChatBotReason(),
                        'is_active' => $phoneNumber->is_active,
                        'is_chatbot_activated' => $phoneNumber->is_chatbot_activated,
                        'has_flow' => $phoneNumber->flow_id !== null,
                        'exchanged_messages_processed' => $exchangedMessagesResult['processed'] ?? 0
                    ]
                );

                return [
                    'success' => true,
                    'type' => 'manual',
                    'chatbot_processed' => false,
                    'reason_skipped' => $phoneNumber->getSkipChatBotReason(),
                    'processed_count' => $exchangedMessagesResult['processed'] ?? 0,
                    'organization_id' => $organization->id,
                    'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                    'exchanged_messages' => [
                        'processed' => $exchangedMessagesResult['processed'] ?? 0,
                        'total_messages' => $exchangedMessagesResult['total_messages'] ?? 0,
                        'success' => $exchangedMessagesResult['success'] ?? false
                    ],
                    'webhook_log_id' => $webhookLogId
                ];
            }

            $results = [];
            $processedCount = 0;

            $incomingMessages = $changeValue->getIncomingMessages($phoneNumber);

            foreach ($incomingMessages as $messageData) {
                $message = new ChangeValueMessage($messageData);

                $webhookData = [
                    'message' => $message->toArray(),
                    'metadata' => $changeValue->metadata,
                    'contacts' => $changeValue->contacts,
                    'organization' => $organization,
                    'phone_number' => $phoneNumber
                ];

                $result = $this->chatBotService->processWebhook($webhookData);
                if ($result['success'] ?? false) {
                    $results[] = $result;
                    $processedCount++;
                }
            }

            return [
                'success' => true,
                'type' => 'chatbot',
                'chatbot_processed' => true,
                'processed_count' => $processedCount,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                'results' => $results,
                'exchanged_messages' => [
                    'processed' => $exchangedMessagesResult['processed'] ?? 0,
                    'total_messages' => $exchangedMessagesResult['total_messages'] ?? 0,
                    'success' => $exchangedMessagesResult['success'] ?? false
                ],
                'webhook_log_id' => $webhookLogId
            ];

        } catch (\Exception $e) {
            DBLog::logError(
                "Error processing webhook message: " . $e->getMessage(),
                "ProcessWebhookMessage",
                $organization->id,
                null,
                [
                    'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                    'webhook_log_id' => $webhookLogId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => 'error',
                'chatbot_processed' => false,
                'processed_count' => 0,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                'webhook_log_id' => $webhookLogId
            ];
        }
    }
}

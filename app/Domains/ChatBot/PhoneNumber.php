<?php

namespace App\Domains\ChatBot;

use App\Domains\Organization;
use Carbon\Carbon;

class PhoneNumber
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $client_id;
    public ?int $flow_id;
    public ?string $phone_number;
    public ?string $name;
    public ?string $description;
    public ?bool $is_active;
    public ?bool $is_chatbot_activated;
    public ?string $whatsapp_phone_number_id;
    public ?string $whatsapp_business_id;
    public ?string $whatsapp_access_token;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Carbon $deleted_at;

    public ?Organization $organization;
    public ?Flow $flow;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?int $client_id,
        ?int $flow_id,
        ?string $phone_number,
        ?string $name,
        ?string $description,
        ?bool $is_active = true,
        ?bool $is_chatbot_activated = true,
        ?string $whatsapp_phone_number_id = null,
        ?string $whatsapp_business_id = null,
        ?string $whatsapp_access_token = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Carbon $deleted_at = null,
        ?Organization $organization = null,
        ?Flow $flow = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->client_id = $client_id;
        $this->flow_id = $flow_id;
        $this->phone_number = $phone_number;
        $this->name = $name;
        $this->description = $description;
        $this->is_active = $is_active;
        $this->is_chatbot_activated = $is_chatbot_activated;
        $this->whatsapp_phone_number_id = $whatsapp_phone_number_id;
        $this->whatsapp_business_id = $whatsapp_business_id;
        $this->whatsapp_access_token = $whatsapp_access_token;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->deleted_at = $deleted_at;
        $this->organization = $organization;
        $this->flow = $flow;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number" => $this->phone_number,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_chatbot_activated" => $this->is_chatbot_activated,
            "whatsapp_phone_number_id" => $this->whatsapp_phone_number_id,
            "whatsapp_business_id" => $this->whatsapp_business_id,
            "whatsapp_access_token" => $this->whatsapp_access_token,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "deleted_at" => $this->deleted_at?->format("Y-m-d H:i:s"),
            "organization" => $this->organization?->toArray(),
            "flow" => $this->flow?->toArray(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number" => $this->phone_number,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_chatbot_activated" => $this->is_chatbot_activated,
            "whatsapp_phone_number_id" => $this->whatsapp_phone_number_id,
            "whatsapp_business_id" => $this->whatsapp_business_id,
            "whatsapp_access_token" => $this->whatsapp_access_token,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number" => $this->phone_number,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_chatbot_activated" => $this->is_chatbot_activated,
            "whatsapp_phone_number_id" => $this->whatsapp_phone_number_id,
            "whatsapp_business_id" => $this->whatsapp_business_id,
            "whatsapp_access_token" => $this->whatsapp_access_token,
        ];
    }

    /**
     * Determine if the ChatBot should process messages for this phone number
     *
     * This method verifies multiple conditions:
     * - Phone number must be active (is_active = true)
     * - ChatBot must be enabled (is_chatbot_activated = true)
     * - A flow must be configured (flow_id is not null)
     *
     * @return bool Returns true only if ALL conditions are met
     */
    public function shouldProcessChatBot(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        if (!$this->is_chatbot_activated) {
            return false;
        }
        if ($this->flow_id === null) {
            return false;
        }
        return true;
    }

    public function getSkipChatBotReason(): string
    {
        if (!$this->is_active) {
            return 'Phone number is inactive';
        }
        if (!$this->is_chatbot_activated) {
            return 'ChatBot is disabled for this phone number';
        }
        if ($this->flow_id === null) {
            return 'No flow configured for this phone number';
        }
        return 'Unknown reason';
    }
}
